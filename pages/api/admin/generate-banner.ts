import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const body = req.body as {
      campaign_id: string;
      campaign_title: string;
      campaign_description: string;
      target_audience: string;
      platform: string;
    };

    const mockGeneratedBanner = {
      id: `banner_${Date.now()}`,
      url: `https://via.placeholder.com/800x400/007bff/ffffff?text=${encodeURIComponent(
        body.campaign_title || 'AI Generated Banner',
      )}`,
      prompt: `Create a professional banner for "${body.campaign_title}" targeting ${body.target_audience} for ${body.platform} platform`,
      created_at: new Date().toISOString(),
    };

    setTimeout(() => {
      res.status(200).json(mockGeneratedBanner);
    }, 2000);
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
