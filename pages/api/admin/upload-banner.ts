import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const body = req.body as {
      banner_id: string;
      campaign_id: string;
      banner_url: string;
    };

    setTimeout(() => {
      res.status(200).json({
        success: true,
        message: 'Banner uploaded to ads manager successfully',
        upload_id: `upload_${Date.now()}`,
        banner_id: body.banner_id,
        campaign_id: body.campaign_id,
      });
    }, 1500);
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
