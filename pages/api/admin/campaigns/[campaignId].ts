import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { campaignId } = req.query;
  const id = Array.isArray(campaignId) ? campaignId[0] : campaignId;

  if (req.method === 'GET') {
    const mockCampaignDetails = {
      id: id,
      title: `Campaign ${id}`,
      description:
        'This is a mock campaign for testing AI media generation functionality',
      status: 'active',
      budget: 1500,
      target_audience: 'Young professionals aged 25-40',
      platform: 'Facebook & Instagram',
    };

    res.status(200).json(mockCampaignDetails);
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
