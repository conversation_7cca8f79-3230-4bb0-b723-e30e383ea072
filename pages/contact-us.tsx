import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser } from 'src/types';
import Button from '@/components/lib/Button';
import Input from '@/components/lib/input/Input';
import MobileContainer from '@/components/lib/MobileContainer';
import { getCommonHeaders } from 'src/actions';
import { submitContactUs } from 'src/actions/profile';
import { showToastMessage } from 'src/modules/toast';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import BackIcon from '@/images/common/back-arrow.svg';
import Head from 'next/head';
import Image from 'next/image';

interface IContactUsProps {
  user?: IGroweasyUser;
}

interface IContactFormData {
  subject: string;
  message: string;
}

const ContactUs = (props: IContactUsProps) => {
  const { user } = props;
  const router = useRouter();

  const [formData, setFormData] = useState<IContactFormData>({
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof IContactFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      formData.subject.trim().length < 2 ||
      formData.message.trim().length < 3
    ) {
      showToastMessage(
        'Subject must be at least 2 characters and message at least 3 characters',
        'error',
      );
      return;
    }

    if (!user?.authToken) {
      showToastMessage('Please login to submit a contact request', 'error');
      void router.push('/login');
      return;
    }

    setIsSubmitting(true);

    try {
      await submitContactUs({
        headers: getCommonHeaders(user),
        data: {
          subject: formData.subject.trim(),
          message: formData.message.trim(),
        },
      });

      showToastMessage('Your message has been sent successfully!', 'success');
      setFormData({ subject: '', message: '' });
    } catch (error) {
      logApiErrorAndShowToastMessage(error as Error, 'ContactUs.handleSubmit');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <>
      <Head>
        <title>Contact Us - GrowEasy</title>
      </Head>
      <MobileContainer>
        <div className="flex flex-col h-full">
          <div className="flex items-center px-4 py-3 border-b border-gray-light">
            <button
              onClick={handleBack}
              className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Go back"
            >
              <BackIcon className="w-5 h-5 text-gray-dark" />
            </button>
            <h1 className="text-lg font-semibold text-black ml-3">
              Contact Us
            </h1>
          </div>

          <div className="flex-1 px-4 py-6 overflow-y-auto">
            <div className="max-w-md mx-auto">
              <div>
                <Image
                  src="/images/logos/groweasy-with-name.svg"
                  width={200}
                  height={100}
                  alt="Contact Us"
                  className="mx-auto my-4"
                />
                <p className="text-gray-dark text-sm mb-6">
                  Have a question or need support? We&apos;re here to help! Send
                  us a message and we&apos;ll get back to you as soon as
                  possible.
                </p>
              </div>

              <form
                onSubmit={(e) => void handleSubmit(e)}
                className="space-y-6"
              >
                <Input
                  label="Subject"
                  type="text"
                  placeholder="What's this about?"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  required
                  disabled={isSubmitting}
                />

                <div className="flex flex-col gap-1">
                  <label htmlFor="contact-message" className="text-xl font-medium">Message</label>
                  <textarea
                    id="contact-message"
                    className="w-full outline-none rounded-lg px-4 py-3 focus:shadow-[inset_0_0_0_2px_#286053] shadow-[inset_0_0_0_1px_#286053] resize-none"
                    placeholder="Tell us more about your question or issue..."
                    value={formData.message}
                    onChange={(e) =>
                      handleInputChange('message', e.target.value)
                    }
                    rows={5}
                    required
                    disabled={isSubmitting}
                  />
                </div>

                <Button
                  type="submit"
                  disabled={
                    isSubmitting ||
                    !formData.subject.trim() ||
                    !formData.message.trim()
                  }
                  className="w-full flex items-center justify-center"
                >
                  {isSubmitting ? (
                    <>
                      <SpinnerLoader size={20} className="mr-2" />
                      Sending...
                    </>
                  ) : (
                    'Send Message'
                  )}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </MobileContainer>
    </>
  );
};

export default ContactUs;
