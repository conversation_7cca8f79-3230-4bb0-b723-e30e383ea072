import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import Image from 'next/image';
import { QueryParams } from 'src/constants';

interface IAiMediaProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

interface ICampaignDetails {
  id: string;
  title: string;
  description: string;
  status: string;
  budget: number;
  target_audience: string;
  platform: string;
}

interface IGeneratedBanner {
  id: string;
  url: string;
  prompt: string;
  created_at: string;
}

const AiMedia = (props: IAiMediaProps) => {
  const { user } = props;
  const router = useRouter();

  const [campaignId, setCampaignId] = useState('');
  const [campaignDetails, setCampaignDetails] =
    useState<ICampaignDetails | null>(null);
  const [generatedBanner, setGeneratedBanner] =
    useState<IGeneratedBanner | null>(null);
  const [isLoadingCampaign, setIsLoadingCampaign] = useState(false);
  const [isGeneratingBanner, setIsGeneratingBanner] = useState(false);
  const [isUploadingBanner, setIsUploadingBanner] = useState(false);
  const [isBannerConfirmed, setIsBannerConfirmed] = useState(false);
  const [step, setStep] = useState<'input' | 'campaign' | 'banner' | 'upload'>(
    'input',
  );
  const [campaignIdError, setCampaignIdError] = useState<string | null>(null);

  useEffect(() => {
    const qpId = router.query[QueryParams.CAMPAIGN_ID];
    const id = Array.isArray(qpId) ? qpId?.[0] : qpId;
    if (id && typeof id === 'string') {
      setCampaignId(id);
    }
  }, [router.query]);

  useEffect(() => {
    if (campaignId) {
      setCampaignIdError(null);
    }
  }, [campaignId]);

  const fetchCampaignDetails = async () => {
    if (!campaignId.trim()) {
      setCampaignIdError('Campaign ID is required');
      return;
    }

    setIsLoadingCampaign(true);
    try {
      const response = await fetch(`/api/admin/campaigns/${campaignId}`, {
        headers: getCommonHeaders(user),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch campaign details');
      }

      const data = (await response.json()) as ICampaignDetails;
      setCampaignDetails(data);
      setStep('campaign');
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'admin.ai-media.fetchCampaignDetails',
      );
      // Fallback: mock data to allow flow during development
      setCampaignDetails({
        id: campaignId,
        title: 'Sample Campaign',
        description:
          'This is a sample campaign for testing AI media generation',
        status: 'active',
        budget: 1000,
        target_audience: 'Young adults 18-35',
        platform: 'Facebook',
      });
      setStep('campaign');
    } finally {
      setIsLoadingCampaign(false);
    }
  };

  const generateBanner = async () => {
    if (!campaignDetails) return;

    setIsGeneratingBanner(true);
    try {
      const response = await fetch('/api/admin/generate-banner', {
        method: 'POST',
        headers: {
          ...getCommonHeaders(user),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          campaign_id: campaignDetails.id,
          campaign_title: campaignDetails.title,
          campaign_description: campaignDetails.description,
          target_audience: campaignDetails.target_audience,
          platform: campaignDetails.platform,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate banner');
      }

      const data = (await response.json()) as IGeneratedBanner;
      setGeneratedBanner(data);
      setIsBannerConfirmed(false);
      setStep('banner');
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'admin.ai-media.generateBanner',
      );
    } finally {
      setIsGeneratingBanner(false);
    }
  };

  const uploadToAdsManager = async () => {
    if (!generatedBanner || !campaignDetails) return;

    setIsUploadingBanner(true);
    try {
      const response = await fetch('/api/admin/upload-banner', {
        method: 'POST',
        headers: {
          ...getCommonHeaders(user),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          banner_id: generatedBanner.id,
          campaign_id: campaignDetails.id,
          banner_url: generatedBanner.url,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to upload banner to ads manager');
      }

      setStep('upload');
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'admin.ai-media.uploadToAdsManager',
      );
    } finally {
      setIsUploadingBanner(false);
    }
  };

  const resetFlow = () => {
    setCampaignId('');
    setCampaignDetails(null);
    setGeneratedBanner(null);
    setIsBannerConfirmed(false);
    setStep('input');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      void fetchCampaignDetails();
    }
  };

  return (
    <MobileContainer>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-8 tracking-tight">
              AI Media Generator
            </h1>

            {step === 'input' && (
              <div className="space-y-6">
                <div>
                  <label
                    htmlFor="campaignId"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Campaign ID
                  </label>
                  <input
                    type="text"
                    id="campaignId"
                    value={campaignId}
                    onChange={(e) => setCampaignId(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter campaign ID"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    Press Enter to fetch campaign details
                  </p>
                </div>

                <button
                  onClick={() => void fetchCampaignDetails()}
                  disabled={!campaignId.trim() || isLoadingCampaign}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isLoadingCampaign ? (
                    <>
                      <SpinnerLoader className="w-5 h-5 mr-2" />
                      Fetching Campaign...
                    </>
                  ) : (
                    'Fetch Campaign Details'
                  )}
                </button>
              </div>
            )}

            {step === 'campaign' && campaignDetails && (
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Campaign Details
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">ID</p>
                      <p className="text-gray-900">{campaignDetails.id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Title</p>
                      <p className="text-gray-900">{campaignDetails.title}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Status
                      </p>
                      <p className="text-gray-900 capitalize">
                        {campaignDetails.status}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Budget
                      </p>
                      <p className="text-gray-900">${campaignDetails.budget}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Platform
                      </p>
                      <p className="text-gray-900">
                        {campaignDetails.platform}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Target Audience
                      </p>
                      <p className="text-gray-900">
                        {campaignDetails.target_audience}
                      </p>
                    </div>
                    <div className="md:col-span-2">
                      <p className="text-sm font-medium text-gray-700">
                        Description
                      </p>
                      <p className="text-gray-900">
                        {campaignDetails.description}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={() => void generateBanner()}
                    disabled={isGeneratingBanner}
                    className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isGeneratingBanner ? (
                      <>
                        <SpinnerLoader className="w-5 h-5 mr-2" />
                        Generating Banner...
                      </>
                    ) : (
                      'Generate AI Banner'
                    )}
                  </button>
                  <button
                    onClick={resetFlow}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Back
                  </button>
                </div>
              </div>
            )}

            {step === 'banner' && generatedBanner && (
              <div className="space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    Generated Banner
                  </h2>
                  <div className="space-y-4">
                    <div className="border rounded-lg overflow-hidden">
                      <Image
                        src={generatedBanner.url}
                        alt="Generated Banner"
                        width={800}
                        height={400}
                        className="w-full h-auto"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Prompt Used
                      </p>
                      <p className="text-gray-900">{generatedBanner.prompt}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Created At
                      </p>
                      <p className="text-gray-900">
                        {new Date(generatedBanner.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={() => void uploadToAdsManager()}
                    disabled={isUploadingBanner}
                    className="flex-1 bg-purple-600 text-white py-3 px-6 rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isUploadingBanner ? (
                      <>
                        <SpinnerLoader className="w-5 h-5 mr-2" />
                        Uploading to Ads Manager...
                      </>
                    ) : (
                      'Upload to Ads Manager'
                    )}
                  </button>
                  <button
                    onClick={() => setStep('campaign')}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Back
                  </button>
                  <button
                    onClick={resetFlow}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Start Over
                  </button>
                </div>
              </div>
            )}

            {step === 'upload' && (
              <div className="text-center space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto mb-4">
                    <svg
                      className="w-8 h-8 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <h2 className="text-xl font-semibold text-green-900 mb-2">
                    Banner Uploaded Successfully!
                  </h2>
                  <p className="text-green-700">
                    The AI-generated banner has been uploaded to the ads manager
                    and is ready to use in your campaign.
                  </p>
                </div>

                <button
                  onClick={resetFlow}
                  className="bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700"
                >
                  Generate Another Banner
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </MobileContainer>
  );
};

export default AiMedia;
